<!-- src/routes/+layout.svelte -->
<script lang="ts">
    import type { LayoutData } from './$types';

    export let data: LayoutData;
</script>

<nav style="display: flex; justify-content: space-between; padding: 1rem; border-bottom: 1px solid #ccc;">
    <a href="/">Home</a>
    {#if data.user}
        <span>Welcome, {data.user.name}</span>
        <form action="/auth/sign-out" method="POST">
            <button type="submit">Log out</button>
        </form>
    {:else}
        <a href="/login">Log in</a>
    {/if}
</nav>

<slot />
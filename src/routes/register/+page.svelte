<!-- src/routes/register/+page.svelte -->
<script lang="ts">
    import { enhance } from '$app/forms';
    import type { RegisterActionData } from '$lib/types';

    export let form: RegisterActionData | null;
</script>

<main style="max-width: 400px; margin: 2rem auto;">
    <h1>Register</h1>

    {#if form?.message}
        <p style="color: red;">{form.message}</p>
    {/if}

    <form method="POST" use:enhance>
        <div style="margin-bottom: 1rem;">
            <label for="name">Name</label>
            <input id="name" name="name" type="text" required value={form?.name ?? ''} style="width: 100%;" />
        </div>
        
        <div style="margin-bottom: 1rem;">
            <label for="email">Email</label>
            <input id="email" name="email" type="email" required value={form?.email ?? ''} style="width: 100%;" />
        </div>

        <div style="margin-bottom: 1rem;">
            <label for="password">Password</label>
            <input id="password" name="password" type="password" required style="width: 100%;" />
        </div>

        <button type="submit">Register</button>
    </form>
</main>
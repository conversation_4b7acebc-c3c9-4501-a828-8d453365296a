// src/routes/register/+page.server.ts
import { auth } from '$lib/server/auth';
import { fail, redirect } from '@sveltejs/kit';
import { APIError } from 'better-auth/api';

/** @type {import('./$types').Actions} */
export const actions = {
	default: async ({ request }) => {
		const formData = await request.formData();
		const name = formData.get('name')?.toString();
		const email = formData.get('email')?.toString();
		const password = formData.get('password')?.toString();

		if (!name || !email || !password) {
			return fail(400, { message: 'All fields are required' });
		}

		try {
			// Wywołujemy rejestrację. Handler w hooks.server.ts zajmie się
			// ustawieniem ciasteczka sesji po automatycznym zalogowaniu.
			await auth.api.signUpEmail({
				body: { name, email, password },
				headers: request.headers
			});
		} catch (error) {
			// <PERSON><PERSON><PERSON> better-auth zwróci błąd (np. użytkownik już istnieje).
			if (error instanceof APIError) {
				return fail(Number(error.status), { message: error.message, email, name });
			}
			// W razie nieoczekiwanego błędu serwera.
			return fail(500, { message: 'Something went wrong.' });
		}

		// Po udanej rejestracji przekieruj na stronę główną.
		throw redirect(303, '/');
	}
};
// src/routes/register/+page.server.ts
import { auth } from '$lib/server/auth';
import { fail, redirect } from '@sveltejs/kit';
import { APIError } from 'better-auth/api';
import { debugCookies, logAuthAction } from '$lib/server/auth-debug';

/** @type {import('./$types').Actions} */
export const actions = {
	default: async ({ request }) => {
		const formData = await request.formData();
		const name = formData.get('name')?.toString();
		const email = formData.get('email')?.toString();
		const password = formData.get('password')?.toString();

		if (!name || !email || !password) {
			logAuthAction('REGISTER', email, false, 'Missing required fields');
			return fail(400, { message: 'All fields are required' });
		}

		try {
			// Debug cookies before registration attempt
			debugCookies({ request } as any, 'Before Registration');

			// Call Better Auth sign up - this will create user and auto-login
			await auth.api.signUpEmail({
				body: { name, email, password },
				headers: request.headers
			});

			// Log successful registration
			logAuthAction('REGISTER', email, true);

		} catch (error) {
			// Log failed registration
			logAuthAction('REGISTER', email, false, error);

			// Handle Better Auth API errors (e.g., user already exists)
			if (error instanceof APIError) {
				return fail(Number(error.status), {
					message: error.message || 'Registration failed',
					email,
					name
				});
			}

			// Handle unexpected server errors
			return fail(500, {
				message: 'Something went wrong during registration. Please try again.',
				email,
				name
			});
		}

		// After successful registration, redirect to home page
		throw redirect(303, '/');
	}
};
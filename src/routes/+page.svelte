<!-- src/routes/+page.svelte -->
<script>
	import { page } from '$app/stores';

	/** @type {import('./$types').PageData} */
	export let data;

	$: user = data.user;
</script>

<main style="padding: 2rem;">
	<h1>Welcome to your Better Auth PoC</h1>
	
	{#if user}
		<div style="background: #f0f9ff; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
			<h2>✅ You are logged in!</h2>
			<p><strong>Name:</strong> {user.name}</p>
			<p><strong>Email:</strong> {user.email}</p>
			<p><strong>User ID:</strong> {user.id}</p>
		</div>
	{:else}
		<div style="background: #fef2f2; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
			<h2>❌ You are not logged in</h2>
			<p>Please <a href="/login">log in</a> or <a href="/register">register</a> to continue.</p>
		</div>
	{/if}
</main>
// Debug endpoint to clear all sessions and cookies
import { auth } from '$lib/server/auth';
import { redirect } from '@sveltejs/kit';
import Database from 'better-sqlite3';

export const actions = {
    default: async ({ cookies }) => {
        // Clear all sessions from database
        const db = new Database('db.sqlite');
        db.prepare("DELETE FROM session").run();
        db.close();
        
        // Clear the session cookie
        cookies.delete('better-auth.session_token', { path: '/' });
        
        console.log('All sessions cleared and cookies deleted');
        
        throw redirect(303, '/');
    }
};

export const load = async () => {
    return {};
};

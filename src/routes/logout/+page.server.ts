// src/routes/logout/+page.server.ts
import { auth } from '$lib/server/auth';
import { redirect } from '@sveltejs/kit';

export const actions = {
    default: async ({ request }) => {
        await auth.api.signOut({ headers: request.headers });
        throw redirect(303, '/login');
    }
}

// Potrzebujemy funkcji load, żeby SvelteKit wiedział, że ta strona istnieje jako endpoint dla akcji
export const load = async () => {};
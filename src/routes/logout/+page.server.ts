// src/routes/logout/+page.server.ts
import { auth } from '$lib/server/auth';
import { redirect } from '@sveltejs/kit';
import { APIError } from 'better-auth/api';
import { debugCookies, logAuthAction } from '$lib/server/auth-debug';

export const actions = {
    default: async ({ request }) => {
        try {
            // Debug cookies before logout attempt
            debugCookies({ request } as any, 'Before Logout');

            // Call Better Auth sign out - this will invalidate the session
            await auth.api.signOut({ headers: request.headers });

            // Log successful logout
            logAuthAction('LOGOUT', undefined, true);

        } catch (error) {
            // Log failed logout
            logAuthAction('LOGOUT', undefined, false, error);

            // Even if logout fails, we should still redirect to login
            // but we could optionally show an error message
            if (error instanceof APIError) {
                console.error('Better Auth logout error:', error.message);
            }
        }

        // Always redirect to login page after logout attempt
        throw redirect(303, '/login');
    }
}

// Load function to make this page accessible as an endpoint for actions
export const load = async () => {};
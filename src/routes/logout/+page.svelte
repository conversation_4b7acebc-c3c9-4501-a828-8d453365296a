<!-- src/routes/logout/+page.svelte -->
<script lang="ts">
	import { enhance } from '$app/forms';
	import { onMount } from 'svelte';

	// Auto-submit the form when the page loads
	onMount(() => {
		const form = document.querySelector('form');
		if (form) {
			form.submit();
		}
	});
</script>

<main style="max-width: 400px; margin: 2rem auto; text-align: center;">
	<h1>Logging out...</h1>
	<p>Please wait while we log you out.</p>
	
	<form method="POST" use:enhance style="display: none;">
		<button type="submit">Logout</button>
	</form>
</main>
// src/routes/login/+page.server.ts
import { auth } from '$lib/server/auth';
import { fail, redirect } from '@sveltejs/kit';
import { APIError } from 'better-auth/api';

/** @type {import('./$types').Actions} */
export const actions = {
	default: async ({ request }) => {
		const formData = await request.formData();
		const email = formData.get('email')?.toString();
		const password = formData.get('password')?.toString();

		if (!email || !password) {
			return fail(400, { message: 'Email and password are required' });
		}

		try {
			// Wywołujemy logowanie. Handler w hooks.server.ts zajmie się
			// przechwyceniem odpowiedzi i ustawieniem ciasteczka sesji.
			await auth.api.signInEmail({
				body: { email, password },
				headers: request.headers
			});
		} catch (error) {
			// <PERSON><PERSON><PERSON> better-auth zwr<PERSON>ci błąd (np. złe hasło), obsłuż go.
			if (error instanceof APIError) {
				return fail(Number(error.status), { message: error.message, email });
			}
			// W razie nieoczekiwanego błędu serwera.
			return fail(500, { message: 'Something went wrong.' });
		}

		// Po udanym logowaniu przekieruj użytkownika na stronę główną.
		throw redirect(303, '/');
	}
};
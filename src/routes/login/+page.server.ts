// src/routes/login/+page.server.ts
import { auth } from '$lib/server/auth';
import { fail, redirect } from '@sveltejs/kit';
import { APIError } from 'better-auth/api';
import { debugCookies, logAuthAction } from '$lib/server/auth-debug';

/** @type {import('./$types').Actions} */
export const actions = {
	default: async ({ request }) => {
		const formData = await request.formData();
		const email = formData.get('email')?.toString();
		const password = formData.get('password')?.toString();

		if (!email || !password) {
			logAuthAction('LOGIN', email, false, 'Missing email or password');
			return fail(400, { message: 'Email and password are required' });
		}

		try {
			// Debug cookies before login attempt
			debugCookies({ request } as any, 'Before Login');

			// Call Better Auth sign in - the svelteKitHandler will handle cookie setting
			await auth.api.signInEmail({
				body: { email, password },
				headers: request.headers
			});

			// Log successful login
			logAuthAction('LOGIN', email, true);

		} catch (error) {
			// Log failed login
			logAuthAction('LOGIN', email, false, error);

			// Handle Better Auth API errors
			if (error instanceof APIError) {
				return fail(Number(error.status), {
					message: error.message || 'Login failed',
					email
				});
			}

			// Handle unexpected server errors
			return fail(500, {
				message: 'Something went wrong during login. Please try again.',
				email
			});
		}

		// After successful login, redirect to home page
		throw redirect(303, '/');
	}
};
// src/hooks.server.ts
import { auth } from '$lib/server/auth';
import { svelteKitHandler } from 'better-auth/svelte-kit';
import type { Handle } from '@sveltejs/kit';
import { sequence } from '@sveltejs/kit/hooks';
import { debugCookies, debugAuthState } from '$lib/server/auth-debug';

// Manual session validation middleware
const sessionMiddleware: Handle = async ({ event, resolve }) => {
	debugCookies(event, 'Session Middleware');

	// Extract session token from cookies
	const cookieHeader = event.request.headers.get('cookie');
	let sessionToken = null;

	if (cookieHeader) {
		const cookies = cookieHeader.split(';').map(c => c.trim());
		for (const cookie of cookies) {
			if (cookie.startsWith('better-auth.session_token=')) {
				sessionToken = cookie.split('=')[1];
				break;
			}
		}
	}

	console.log('Extracted session token:', sessionToken);

	if (sessionToken) {
		try {
			// Manually validate session using Better Auth
			const sessionData = await auth.api.getSession({
				headers: new Headers({
					'cookie': `better-auth.session_token=${sessionToken}`
				})
			});

			console.log('Manual session validation result:', sessionData);

			if (sessionData) {
				event.locals.user = sessionData.user;
				event.locals.session = sessionData.session;
			} else {
				event.locals.user = null;
				event.locals.session = null;
			}
		} catch (error) {
			console.error('Manual session validation error:', error);
			event.locals.user = null;
			event.locals.session = null;
		}
	} else {
		event.locals.user = null;
		event.locals.session = null;
	}

	debugAuthState(event.locals.user, event.locals.session, 'After Manual Session Validation');

	return resolve(event);
};

// Better Auth handler for API routes and cookie setting
const betterAuthHandler: Handle = async ({ event, resolve }) => {
	const response = await svelteKitHandler({ event, resolve, auth });

	// Debug response cookies
	if (process.env.NODE_ENV === 'development') {
		const setCookieHeader = response.headers.get('set-cookie');
		if (setCookieHeader) {
			console.log('Response Set-Cookie header:', setCookieHeader);
		}
	}

	return response;
};

// Combine handlers: session validation first, then Better Auth API handling
export const handle = sequence(sessionMiddleware, betterAuthHandler);
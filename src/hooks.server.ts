// src/hooks.server.ts
import { auth } from '$lib/server/auth';
import { svelteKitHandler } from 'better-auth/svelte-kit';
import type { Handle } from '@sveltejs/kit';
import { debugCookies, debugAuthState } from '$lib/server/auth-debug';

// Use the Better Auth SvelteKit handler which automatically handles both
// cookie setting AND session validation/locals population
export const handle: Handle = async ({ event, resolve }) => {
	// Debug incoming cookies
	debugCookies(event, 'Before Better Auth Handler');

	// The svelteKitHandler will:
	// 1. Handle authentication API routes
	// 2. Set cookies for authentication actions
	// 3. Populate event.locals.user and event.locals.session
	const response = await svelte<PERSON>it<PERSON>andler({ event, resolve, auth });

	// Debug response cookies
	if (process.env.NODE_ENV === 'development') {
		const setCookieHeader = response.headers.get('set-cookie');
		if (setCookieHeader) {
			console.log('Response Set-Cookie header:', setCookieHeader);
		}
	}

	// Debug auth state after Better Auth handler
	debugAuthState(event.locals.user, event.locals.session, 'After Better Auth Handler');

	return response;
};
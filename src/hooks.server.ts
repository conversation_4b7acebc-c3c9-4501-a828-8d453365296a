// src/hooks.server.ts
import { auth } from '$lib/server/auth';
import { svelteKitHandler } from 'better-auth/svelte-kit';
import type { Handle } from '@sveltejs/kit';

// Ta funkcja jest kluczowa. Przechwytuje ona odpowiedzi od akcji SvelteKit,
// sprawdza, czy better-auth chce dodać nagłówek Set-Cookie, i robi to za nas.
export const handle: Handle = async ({ event, resolve }) => {
	return svelteKitHandler({ event, resolve, auth });
};
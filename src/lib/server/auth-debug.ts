// Debug utilities for Better Auth integration
import type { RequestEvent } from '@sveltejs/kit';

export function debugCookies(event: RequestEvent, context: string) {
	if (process.env.NODE_ENV === 'development') {
		console.log(`\n=== Cookie Debug: ${context} ===`);
		
		// Log incoming cookies
		const cookieHeader = event.request.headers.get('cookie');
		console.log('Incoming cookies:', cookieHeader || 'None');
		
		// Parse and display individual cookies
		if (cookieHeader) {
			const cookies = cookieHeader.split(';').map(c => c.trim());
			cookies.forEach(cookie => {
				if (cookie.includes('better-auth')) {
					console.log('  Better Auth cookie:', cookie);
				}
			});
		}
		
		console.log('=== End Cookie Debug ===\n');
	}
}

export function debugAuthState(user: any, session: any, context: string) {
	if (process.env.NODE_ENV === 'development') {
		console.log(`\n=== Auth State Debug: ${context} ===`);
		console.log('User:', user ? { id: user.id, email: user.email, name: user.name } : 'null');
		console.log('Session:', session ? { id: session.id, expiresAt: session.expiresAt } : 'null');
		console.log('=== End Auth State Debug ===\n');
	}
}

export function logAuthAction(action: string, email?: string, success: boolean = true, error?: any) {
	const timestamp = new Date().toISOString();
	const status = success ? '✅ SUCCESS' : '❌ FAILED';
	
	console.log(`[${timestamp}] ${status} - ${action}${email ? ` for ${email}` : ''}`);
	
	if (!success && error) {
		console.error('Error details:', error);
	}
}

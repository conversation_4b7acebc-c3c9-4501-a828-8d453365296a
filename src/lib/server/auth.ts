import { betterAuth } from 'better-auth';
import Database from 'better-sqlite3';

// Inicjalizujemy bazę danych SQLite w pliku.
// Plik `db.sqlite` zostanie utworzony automatycznie.
const db = new Database('db.sqlite');

export const auth = betterAuth({
	database: db, // Przekazujemy instancję bazy danych
	emailAndPassword: {
		enabled: true // Włączamy logowanie na email i hasło
	},
	// Basic session configuration
	session: {
		cookieCache: {
			enabled: true,
			maxAge: 60 * 60 * 24 * 7 // 7 days
		}
	},
	// Base URL for the application
	baseURL: "http://localhost:5174",
	// Trusted origins for development
	trustedOrigins: ["http://localhost:5173", "http://localhost:5174", "http://localhost:4173"]
});
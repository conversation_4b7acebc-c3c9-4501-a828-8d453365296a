// Simple script to check the database state
import Database from 'better-sqlite3';

const db = new Database('db.sqlite');

console.log('=== Database Tables ===');
const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
console.log('Tables:', tables.map(t => t.name));

console.log('\n=== Users ===');
try {
    const users = db.prepare("SELECT id, email, name, createdAt FROM user").all();
    console.log('Users:', users);
} catch (e) {
    console.log('Users table error:', e.message);
}

console.log('\n=== Sessions ===');
try {
    const sessions = db.prepare("SELECT id, userId, expiresAt, createdAt FROM session").all();
    console.log('Sessions:', sessions);
} catch (e) {
    console.log('Sessions table error:', e.message);
}

db.close();
